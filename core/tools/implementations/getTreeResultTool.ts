import { ToolImpl } from ".";
import { getTreeResult } from "../../util/tree";
import { getOptionalStringArg } from "../parseArgs";
import { inferResolvedUriFromRelativePath } from "../../util/ideUtils";

export const getTreeResultToolImpl: ToolImpl = async (args, extras) => {
  const targetDir = getOptionalStringArg(args, "targetDir", true) ?? "";

  const resolvedTargetDir = await inferResolvedUriFromRelativePath(
    targetDir,
    extras.ide,
  );

  try {
    const result = await getTreeResult(extras.ide, resolvedTargetDir);

    if (result.error) {
      return [
        {
          type: "error",
          name: "Tree Structure Error",
          description: "Error occurred while getting tree structure",
          content: `Failed to get tree structure: ${result.error}`,
        },
      ];
    }

    return [
      {
        type: "success",
        name: "Directory Tree Structure",
        description: resolvedTargetDir
          ? `Tree structure of directory: ${resolvedTargetDir}`
          : "Tree structure of workspace root directory",
        content: result.content,
      },
    ];
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);

    return [
      {
        type: "error",
        name: "Tree Structure Error",
        description: "Error occurred while getting tree structure",
        content: `Failed to get tree structure: ${errorMessage}`,
      },
    ];
  }
};
