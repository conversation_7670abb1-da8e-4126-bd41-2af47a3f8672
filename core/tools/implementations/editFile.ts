import { resolveRelativePathInDir } from "../../util/ideUtils";
import { ToolImpl } from ".";
import { getStringArg } from "../parseArgs";

export const editFileImpl: ToolImpl = async (args, extras) => {
  let filepath: string;
  let changes: string;

  try {
    filepath = getStringArg(args, "filepath", false); // filepath cannot be empty
    changes = getStringArg(args, "changes", false); // changes cannot be empty
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    return [
      {
        name: "Edit File Failed",
        description: "Edit File Failed: Invalid arguments provided",
        content: errorMessage,
        type: "error",
      },
    ];
  }

  const resolvedFilepath = await resolveRelativePathInDir(filepath, extras.ide);
  if (!resolvedFilepath) {
    return [
      {
        name: "Edit File Failed",
        description: `File ${filepath} does not exist`,
        content: `${filepath} does not exist`,
        type: "error",
      },
    ];
  }

  try {
    // Write the changes to the file
    await extras.ide.writeFile(resolvedFilepath, changes);
    await extras.ide.saveFile(resolvedFilepath);

    // Refresh codebase index if available
    if (extras.codeBaseIndexer) {
      void extras.codeBaseIndexer?.refreshCodebaseIndexFiles([
        resolvedFilepath,
      ]);
    }

    return [
      {
        name: "File Edited Successfully",
        description: `Successfully edited ${filepath}`,
        content: `File ${filepath} has been edited with the provided changes.`,
        type: "success",
        uri: {
          type: "file",
          value: resolvedFilepath,
        },
      },
    ];
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    return [
      {
        name: "Edit File Failed",
        description: `Failed to edit ${filepath}`,
        content: `Failed to edit file ${filepath}: ${errorMessage}`,
        type: "error",
      },
    ];
  }
};
