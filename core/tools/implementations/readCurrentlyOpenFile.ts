import { getUriDescription } from "../../util/uri";

import { ToolImpl } from ".";

export const readCurrentlyOpenFileImpl: ToolImpl = async (args, extras) => {
  const result = await extras.ide.getCurrentFile();
  if (!result) {
    return [
      {
        name: "No File Currently Open",
        description: "No file is currently active in the IDE",
        content: "",
        type: "success",
      },
    ];
  }
  const workspaceDirs = await extras.ide?.getWorkspaceDirs();
  if (workspaceDirs) {
    const { relativePathOrBasename, baseName } =
      getUriDescription(result?.path, workspaceDirs) || {};
    return [
      {
        name: `Currently open file: ${baseName}`,
        description: `Successfully read the currently active file: ${baseName}`,
        content: `\`\`\`${relativePathOrBasename}\n${result?.contents}\n\`\`\``,
        uri: {
          type: "file",
          value: result?.path,
        },
        type: "success",
      },
    ];
  } else {
    return [
      {
        name: "Workspace Directory Resolution Failed",
        description: "Unable to resolve the workspace directory",
        content: "Failed to resolve the workspace directory.",
        type: "error",
      },
    ];
  }
  /*
  if (result) {
    await throwIfFileExceedsHalfOfContext(
      result.path,
      result.contents,
      extras.config.selectedModelByRole.chat,
    );

    const { relativePathOrBasename, last2Parts, baseName } = getUriDescription(
      result.path,
      await extras.ide.getWorkspaceDirs(),
    );

    return [
      {
        name: `Current file: ${baseName}`,
        description: last2Parts,
        content: `\`\`\`${relativePathOrBasename}\n${result.contents}\n\`\`\``,
        uri: {
          type: "file",
          value: result.path,
        },
      },
    ];
  } else {
    return [
      {
        name: `No Current File`,
        description: "",
        content: "There are no files currently open.",
      },
    ];
  }*/
};
