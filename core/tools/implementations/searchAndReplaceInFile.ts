import { findSearchMatch } from "../../edit/searchAndReplace/findSearchMatch";
import { parseAllSearchReplaceBlocks } from "../../edit/searchAndReplace/parseSearchReplaceBlock";
import { resolveRelativePathInDir } from "../../util/ideUtils";
import { ToolImpl } from ".";
import { getStringArg } from "../parseArgs";

export const searchAndReplaceInFileImpl: ToolImpl = async (args, extras) => {
  let filepath: string;

  try {
    filepath = getStringArg(args, "filepath");
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    return [
      {
        name: "Search and Replace Failed",
        description: "Search and Replace Failed: Invalid arguments provided",
        content: errorMessage,
        type: "error",
      },
    ];
  }

  const diffs = args.diffs;
  if (!diffs || !Array.isArray(diffs)) {
    return [
      {
        name: "Search and Replace Failed",
        description: "Search and Replace Failed: Invalid diffs argument",
        content:
          "`diffs` argument is required and must be an array for search and replace operation.",
        type: "error",
      },
    ];
  }

  // Resolve the file path
  const resolvedFilepath = await resolveRelativePathInDir(filepath, extras.ide);
  if (!resolvedFilepath) {
    return [
      {
        name: "Search and Replace Failed",
        description: `File ${filepath} does not exist`,
        content: `File ${filepath} does not exist`,
        type: "error",
      },
    ];
  }

  // Parse all search/replace blocks from all diff strings
  const allBlocks = [];
  for (let diffIndex = 0; diffIndex < diffs.length; diffIndex++) {
    const blocks = parseAllSearchReplaceBlocks(diffs[diffIndex]);
    if (blocks.length === 0) {
      return [
        {
          name: "Search and Replace Failed",
          description: `No complete search/replace blocks found in diff ${diffIndex + 1}`,
          content: `No complete search/replace blocks found in diff ${diffIndex + 1}`,
          type: "error",
        },
      ];
    }
    allBlocks.push(...blocks);
  }

  if (allBlocks.length === 0) {
    return [
      {
        name: "Search and Replace Failed",
        description: "No complete search/replace blocks found in any diffs",
        content: "No complete search/replace blocks found in any diffs",
        type: "error",
      },
    ];
  }

  try {
    // Read the current file content
    let currentContent = await extras.ide.readFile(resolvedFilepath);

    // Apply all replacements sequentially to build the final content
    for (let i = 0; i < allBlocks.length; i++) {
      const block = allBlocks[i];
      const { searchContent, replaceContent } = block;

      // Find the search content in the current state of the file
      const match = findSearchMatch(currentContent, searchContent || "");

      if (!match) {
        return [
          {
            name: "Search and Replace Failed",
            description: `Search content not found in block ${i + 1}`,
            content: `Search content not found in block ${i + 1}:\n${searchContent}`,
            type: "error",
          },
        ];
      }

      // Apply the replacement
      currentContent =
        currentContent.substring(0, match.startIndex) +
        (replaceContent || "") +
        currentContent.substring(match.endIndex);
    }

    // Write the final content to the file
    await extras.ide.writeFile(resolvedFilepath, currentContent);
    await extras.ide.saveFile(resolvedFilepath);

    // Refresh codebase index if available
    if (extras.codeBaseIndexer) {
      void extras.codeBaseIndexer?.refreshCodebaseIndexFiles([
        resolvedFilepath,
      ]);
    }

    return [
      {
        name: "Search and Replace Completed",
        description: `Successfully applied search and replace operations to ${filepath}`,
        content: `Successfully applied ${allBlocks.length} search and replace operation(s) to ${filepath}`,
        type: "success",
        uri: {
          type: "file",
          value: resolvedFilepath,
        },
      },
    ];
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    return [
      {
        name: "Search and Replace Failed",
        description: `Failed to apply search and replace to ${filepath}`,
        content: `Failed to apply search and replace: ${errorMessage}`,
        type: "error",
      },
    ];
  }
};
