import { resolveRelativePathInDir } from "../../util/ideUtils";
import { getUriPathBasename } from "../../util/uri";

import { ToolImpl } from ".";
import { getStringArg } from "../parseArgs";

export const readFileImpl: ToolImpl = async (args, extras) => {
  const filepath = getStringArg(args, "filepath", true);
  if (!filepath) {
    return [
      {
        name: "File Read Failed",
        description: "Invalid file path provided",
        content: "Please provide a valid relative file path to read",
        type: "error",
      },
    ];
  }

  const firstUriMatch = await resolveRelativePathInDir(filepath, extras.ide);
  if (!firstUriMatch) {
    // throw new Error(
    //   `File "${filepath}" does not exist. You might want to check the path and try again.`,
    // );
    return [
      {
        name: "File not found",
        description: "The specified file does not exist",
        content: `Could not find file ${filepath}. Please verify the path is correct and relative to the workspace root.`,
        type: "error",
      },
    ];
  }
  const content = await extras.ide.readFile(firstUriMatch);
  if (content === null || content === undefined) {
    return [
      {
        name: `File Read Error: "${filepath}"`,
        description: "Unable to read file content",
        content: `Failed to read content from "${filepath}". The file may be binary, corrupted, or inaccessible.`,
        type: "error",
      },
    ];
  }
  return [
    {
      name: `File ${filepath} read successfully.`,
      description: `Successfully read file content of file ${getUriPathBasename(filepath)}`,
      content,
      uri: {
        type: "file",
        value: firstUriMatch,
      },
      type: "success",
    },
  ];

  /*await throwIfFileExceedsHalfOfContext(
    filepath,
    content,
    extras.config.selectedModelByRole.chat,
  );

  return [
    {
      name: getUriPathBasename(firstUriMatch),
      description: filepath,
      content,
      uri: {
        type: "file",
        value: firstUriMatch,
      },
    },
  ];*/
};
