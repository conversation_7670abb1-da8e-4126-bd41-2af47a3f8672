export enum BuiltInToolNames {
  ReadFile = "readFile",
  EditExistingFile = "editExistingFile",
  SearchAndReplaceInFile = "searchAndReplaceInFile",
  ReadCurrentlyOpenFile = "readCurrentlyOpenFile",
  CreateNewFile = "createNewFile",
  RunTerminalCommand = "runTerminalCommand",
  GrepSearch = "grepSearch",
  FileGlobSearch = "fileGlobSearch",
  SearchWeb = "searchWeb",
  ViewDiff = "viewDiff",
  LSTool = "lsTool",
  CreateRuleBlock = "createRuleBlock",
  RequestRule = "requestRule",
  FetchUrlContent = "fetchUrlContent",
  CodebaseTool = "codebaseTool",
  GetTreeResult = "getTreeResult",
  FileExists = "fileExists",
  GetFileProblems = "getFileProblems",
  GetProposedChanges = "getProposedChanges",
  ListDir = "listDir",

  // excluded from allTools for now
  ViewRepoMap = "viewRepoMap",
  ViewSubdirectory = "viewSubdirectory",
}

export const BUILT_IN_GROUP_NAME = "Built-In";

export const CLIENT_TOOLS_IMPLS = [
  BuiltInToolNames.EditExistingFile,
  BuiltInToolNames.SearchAndReplaceInFile,
];
