import { Tool } from "../..";
import { BUILT_IN_GROUP_NAME, BuiltInToolNames } from "../builtIn";
import { createSystemMessageExampleCall } from "../systemMessageTools/buildToolsSystemMessage";

export const createNewFileTool: Tool = {
  type: "function",
  displayTitle: "Create New File",
  wouldLikeTo: "create a new file at {{{ filepath }}}",
  isCurrently: "creating a new file at {{{ filepath }}}",
  hasAlready: "created a new file at {{{ filepath }}}",
  group: BUILT_IN_GROUP_NAME,
  readonly: false,
  isInstant: true,
  function: {
    name: BuiltInToolNames.CreateNewFile,
    description: `
    Create a new file using the specified relative path and content. Only use this when a file doesn't exist. So you should call the 'fileExists' tool first to determine if the specified file exists at the given path.

    If the file already exists, carefully consider whether to proceed with calling the 'get_proposed_changes' tool or other tools.
    
    If the file does not exist, proceed to create a new file using the specified path and content.
   
    IMPORTANT: If the goal corresponds to the result of the \`verify_code\` tool above, directly pass the \`code_id\` in the result to the \`newCodeId\` input parameter and leave \`contents\` empty.`,
    // "Create a new file. Only use this when a file doesn't exist and should be created",
    parameters: {
      type: "object",
      required: ["filepath", "contents"],
      properties: {
        filepath: {
          type: "string",
          description:
            "The path where the new file should be created, relative to the root of the workspace",
        },
        contents: {
          type: "string",
          description: "The contents to write to the new file",
        },
        newCodeId: {
          type: "string",
          description:
            "The `code_id` of the result of the `verify_code` tool above. REMEMBER to ONLY obtain it from the previous messages, do not generate it yourself. If there is no `verify_code` called above, just be empty.",
        },
      },
    },
  },
  systemMessageDescription: createSystemMessageExampleCall(
    BuiltInToolNames.CreateNewFile,
    `To create a NEW file, use the ${BuiltInToolNames.CreateNewFile} tool with the relative filepath and new contents. For example, to create a file located at 'path/to/file.txt', you would respond with:`,
    [
      ["filepath", "path/to/the_file.txt"],
      ["contents", "Contents of the file"],
    ],
  ),
};
