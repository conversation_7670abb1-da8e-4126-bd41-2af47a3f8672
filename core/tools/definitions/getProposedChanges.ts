import { Tool } from "../..";
import { BUILT_IN_GROUP_NAME, BuiltInToolNames } from "../builtIn";
import { createSystemMessageExampleCall } from "../systemMessageTools/buildToolsSystemMessage";

export const getProposedChangesTool: Tool = {
  type: "function",
  displayTitle: "Get Proposed Changes",
  wouldLikeTo: "propose changes for {{{ targetFile }}}",
  isCurrently: "generating proposed changes for {{{ targetFile }}}",
  hasAlready: "generated proposed changes for {{{ targetFile }}}",
  readonly: true,
  isInstant: false,
  group: BUILT_IN_GROUP_NAME,
  function: {
    name: BuiltInToolNames.GetProposedChanges,
    description: `
      This tool helps you propose edits to an existing file by returning the edited content WITHOUT modifying the file directly.
      It only returns the proposed changes for review.

      This tool is designed for:
      - Reviewing changes before applying them
      - Getting suggestions for code modifications
      - Planning file edits with precise control
      - Working with verified code from previous verify_code tool results

      IMPORTANT NOTES:
      - This tool does NOT modify files directly - it only returns proposed changes
      - Always specify the target file first in your workflow
      - Minimize unchanged code in your input - focus only on the lines that need editing
      - If the goal corresponds to the result of the verify_code tool above, directly pass the "code_id" in the result to the "changedCodeId" input parameter and leave "originalCode" empty.

      Workflow:
      1. Specify the following arguments before the others: [targetFile].
      2. Provide either original code to edit OR a verified code ID
      3. Describe your editing goal clearly
    `.trim(),
    parameters: {
      type: "object",
      properties: {
        targetFile: {
          type: "string",
          description:
            "The relative path of the target file for reference. This helps identify which file the changes are intended for, but no actual file modifications will occur. Always specify the target file as the first argument. You can only use a relative path in the workspace.",
        },
        goal: {
          type: "string",
          description:
            "A single sentence instruction describing what you are going to do for the sketched edit. This is used to assist the less intelligent model in generating the edit. Please use the first person to describe what you are going to do. Don't repeat what you have said previously in normal messages. And use it to disambiguate uncertainty in the edit.",
        },
        originalCode: {
          type: "string",
          description:
            "Specify ONLY the precise lines of code that you wish to edit. **NEVER specify or write out unchanged code**. The tool will return the proposed changes without modifying files. If `changedCodeId` is not null, JUST leave this parameter empty.",
        },
        changedCodeId: {
          type: "string",
          description:
            "The `code_id` of the result of the `verify_code` tool above. REMEMBER to ONLY obtain it from the previous messages, do not generate it yourself. If there is no `verify_code` called above, just be empty.",
        },
      },
    },
  },
  systemMessageDescription: createSystemMessageExampleCall(
    BuiltInToolNames.GetProposedChanges,
    `To get proposed changes for a file, use the ${BuiltInToolNames.GetProposedChanges} tool. You must specify the target file and goal. For example, to propose changes to 'src/main.ts', you would respond with:`,
    [
      ["targetFile", "src/main.ts"],
      ["goal", "Add error handling to the main function"],
      ["originalCode", "function main() { ... }"],
    ],
  ),
};
