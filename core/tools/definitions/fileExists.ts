import { Tool } from "../..";
import { BUILT_IN_GROUP_NAME, BuiltInToolNames } from "../builtIn";
import { createSystemMessageExampleCall } from "../systemMessageTools/buildToolsSystemMessage";

export const fileExistsTool: Tool = {
  type: "function",
  displayTitle: "Check File Exists",
  wouldLikeTo: "check if {{{ filepath }}} exists",
  isCurrently: "checking if {{{ filepath }}} exists",
  hasAlready: "checked if {{{ filepath }}} exists",
  readonly: true,
  isInstant: true,
  group: BUILT_IN_GROUP_NAME,
  function: {
    name: BuiltInToolNames.FileExists,
    description: `
      Check whether a file exists at the specified relative path within the workspace.

      This tool should be used before performing any file operations such as:
      - Creating a new file
      - Reading file contents
      - Editing or modifying a file
      - Deleting a file

      Always verify file existence first to prevent errors and ensure proper workflow.
    `.trim(),
    parameters: {
      type: "object",
      required: ["filepath"],
      properties: {
        filepath: {
          type: "string",
          description:
            "File path, relative to the root of the workspace (NOT uri or absolute path)",
        },
      },
    },
  },
  systemMessageDescription: createSystemMessageExampleCall(
    BuiltInToolNames.FileExists,
    `To check if a file exists, use the ${BuiltInToolNames.FileExists} tool with the relative filepath. For example, to check if a file located at 'src/components/Button.tsx' exists, you would respond with:`,
    [["filepath", "src/components/Button.tsx"]],
  ),
};
