import { DiffLine } from "../index.js";

import { LineStream } from "./util.js";
import { myersDiff } from "./myers";

/**
 * https://blog.jcoglan.com/2017/02/12/the-myers-diff-algorithm-part-1/
 * Invariants:
 * - new + same = newLines.length
 * - old + same = oldLinesCopy.length
 * ^ (above two guarantee that all lines get represented)
 * - Lines are always output in order, at least among old and new separately
 * - Old lines in a hunk are always output before the new lines
 */
export async function* streamDiff(
  oldLines: string[],
  newLines: LineStream,
): AsyncGenerator<DiffLine> {
  // 收集所有新行
  const allNewLines: string[] = [];
  const temp = oldLines.length > 0 ? oldLines.length : 1;
  for await (const line of newLines) {
    allNewLines.push(line);
  }

  // 使用 Myers 算法计算差异
  const diff = myersDiff(oldLines.join("\n"), allNewLines.join("\n"));

  // 输出差分结果
  for (const diffLine of diff) {
    yield diffLine;
  }
}
