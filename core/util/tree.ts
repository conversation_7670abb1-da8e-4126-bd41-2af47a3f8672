#!/usr/bin/env node

import { execSync } from "child_process";
import * as path from "path";
import * as fs from "fs";
import * as process from "process";
import { IDE } from "..";
import { localPathOrUriToPath } from "./pathToUri";
// import { promisify } from "util";
//
// const execAsync = promisify(exec);

interface TreeItem {
  type: "dir" | "file";
  name: string;
}

interface FileInfo {
  files: string[];
  isGitRepo: boolean;
}

interface TreeStructure {
  tree: Map<string, TreeItem[]>;
  dirCount: number;
  fileCount: number;
}

interface TreeResult {
  content: string;
  error: string;
}

// 递归扫描目录获取所有文件
function scanDirectory(dirPath: string, basePath: string = ""): string[] {
  const files: string[] = [];

  try {
    const items = fs.readdirSync(dirPath, { withFileTypes: true });

    for (const item of items) {
      // 跳过隐藏文件和常见的忽略目录
      if (item.name.startsWith(".") && item.name !== ".gitignore") {
        continue;
      }
      if (
        ["node_modules", ".git", "dist", "build", "coverage"].includes(
          item.name,
        )
      ) {
        continue;
      }

      const relativePath = basePath ? `${basePath}/${item.name}` : item.name;

      if (item.isDirectory()) {
        const subFiles = scanDirectory(
          path.join(dirPath, item.name),
          relativePath,
        );
        files.push(...subFiles);
      } else if (item.isFile()) {
        files.push(relativePath);
      }
    }
  } catch (error) {
    const err = error as Error;
    console.error(`错误: 无法读取目录 ${dirPath}:`, err.message);
  }

  return files.sort();
}

// 获取git跟踪的文件列表，如果不是git仓库则扫描目录
function getFiles(targetDir: string = "."): FileInfo {
  try {
    // 首先尝试使用git ls-files
    const command = `git ls-files "${targetDir}"`;

    const output = execSync(command, {
      encoding: "utf8",
      stdio: ["pipe", "pipe", "ignore"],
    });
    const gitFiles = output
      .trim()
      .split("\n")
      .filter((f: string) => f.trim())
      .sort();

    if (gitFiles.length > 0 && gitFiles[0] !== "") {
      return { files: gitFiles, isGitRepo: true };
    }
  } catch (error) {
    // git命令失败，不是git仓库或没有git
  }

  // 如果不是git仓库，扫描文件系统
  const resolvedDir = path.resolve(targetDir);

  if (!fs.existsSync(resolvedDir)) {
    console.error(`错误: 目录不存在: ${targetDir}`);
    process.exit(1);
  }

  if (!fs.statSync(resolvedDir).isDirectory()) {
    console.error(`错误: ${targetDir} 不是一个目录`);
    process.exit(1);
  }

  const files = scanDirectory(resolvedDir);
  return { files, isGitRepo: false };
}

// 构建树形结构
function buildTreeStructure(
  files: string[],
  targetDir: string = ".",
): TreeStructure {
  const tree = new Map<string, TreeItem[]>();
  const allDirs = new Set<string>();

  for (const filePath of files) {
    let processedPath = filePath;

    // 去掉目标目录前缀
    if (targetDir !== "." && filePath.startsWith(targetDir + "/")) {
      processedPath = filePath.substring(targetDir.length + 1);
    } else if (targetDir !== "." && filePath === targetDir) {
      continue;
    }

    // 分解路径
    const parts = processedPath.split("/");

    // 添加所有目录路径
    for (let i = 0; i < parts.length - 1; i++) {
      const dirPath = parts.slice(0, i + 1).join("/");
      allDirs.add(dirPath);
      const parent = i > 0 ? parts.slice(0, i).join("/") : "";

      if (!tree.has(parent)) {
        tree.set(parent, []);
      }

      const items = tree.get(parent)!;
      if (
        !items.some((item) => item.type === "dir" && item.name === parts[i])
      ) {
        items.push({ type: "dir", name: parts[i] });
      }
    }

    // 添加文件
    const parent = parts.length > 1 ? parts.slice(0, -1).join("/") : "";
    if (!tree.has(parent)) {
      tree.set(parent, []);
    }
    tree.get(parent)!.push({ type: "file", name: parts[parts.length - 1] });
  }

  // 对每个目录的内容进行排序（目录在前，文件在后，同类型按字母序）
  for (const [key, items] of tree) {
    items.sort((a, b) => {
      if (a.type !== b.type) {
        return a.type === "dir" ? -1 : 1;
      }
      return a.name.localeCompare(b.name);
    });
  }

  const dirCount = allDirs.size;
  const fileCount = Array.from(tree.values())
    .flat()
    .filter((item) => item.type === "file").length;

  return { tree, dirCount, fileCount };
}

// 递归构建树形结构的字符串表示
function buildTreeString(
  tree: Map<string, TreeItem[]>,
  currentPath: string = "",
  prefix: string = "",
): string {
  const items = tree.get(currentPath) || [];
  let output = "";

  for (let i = 0; i < items.length; i++) {
    const item = items[i];
    const isLast = i === items.length - 1;

    // 选择合适的前缀字符
    const currentPrefix = isLast ? "└── " : "├── ";
    const nextPrefix = isLast ? "    " : "│   ";

    output += `${prefix}${currentPrefix}${item.name}\n`;

    // 如果是目录，递归处理子目录
    if (item.type === "dir") {
      const newPath = currentPath ? `${currentPath}/${item.name}` : item.name;
      output += buildTreeString(tree, newPath, prefix + nextPrefix);
    }
  }

  return output;
}

async function getTreeResult(
  ide: IDE,
  targetDir?: string,
): Promise<TreeResult> {
  try {
    // 获取文件列表（git或文件系统扫描）
    const urlToPath = targetDir
      ? localPathOrUriToPath(targetDir)
      : localPathOrUriToPath((await ide.getWorkspaceDirs())[0]);

    // 检查是否存在 tree 命令，优先使用系统命令
    if (hasTreeCommand()) {
      try {
        const treeOutput = await getTreeFromCommand(urlToPath);
        return {
          content: treeOutput,
          error: "",
        };
      } catch (treeError: any) {
        console.warn(
          "Tree command failed, falling back to custom implementation:",
          treeError.message,
        );
        // 如果 tree 命令失败，继续使用自定义实现
      }
    }

    const { files } = getFiles(urlToPath);
    if (files.length === 0 || (files.length === 1 && files[0] === "")) {
      return {
        content: "",
        error: "文件夹为空",
      };
    }
    // 构建树形结构
    const { tree } = buildTreeStructure(files, targetDir);

    return {
      content: buildTreeString(tree),
      error: "",
    };
  } catch (e: any) {
    console.error("Failed to get tree string:", e);
    return {
      content: "",
      error: e.message || e.toString(),
    };
  }
}

// 使用系统 tree 命令获取目录结构
async function getTreeFromCommand(dirPath: string): Promise<string> {
  try {
    return execSync(`tree --gitignore ${dirPath}`, {
      encoding: "utf8",
      maxBuffer: 1024 * 1024, // 1MB buffer
    });
  } catch (error: any) {
    throw new Error(`Tree command failed: ${error.message}`);
  }
}

// 检查系统是否存在 tree 命令
function hasTreeCommand(): boolean {
  try {
    const command = process.platform === "win32" ? "where tree" : "which tree";
    const result = execSync(command, { encoding: "utf8", stdio: "pipe" });

    // 对结果进行处理来判断
    const output = result.toString().trim();
    return (
      output.length > 0 &&
      !output.includes("not found") &&
      !output.includes("could not find")
    );
  } catch (error: any) {
    // 如果命令执行失败，再检查错误信息
    if (error.stdout) {
      const stdout = error.stdout.toString().trim();
      return stdout.length > 0 && !stdout.includes("not found");
    }
    return false;
  }
}

export { getFiles, buildTreeStructure, getTreeResult, buildTreeString };
export type { TreeItem, FileInfo, TreeStructure, TreeResult };
