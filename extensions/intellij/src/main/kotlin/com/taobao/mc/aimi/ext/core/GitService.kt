package com.taobao.mc.aimi.ext.core

import com.intellij.execution.configurations.GeneralCommandLine
import com.intellij.execution.util.ExecUtil
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.project.Project
import com.intellij.openapi.project.guessProjectDir
import com.taobao.mc.aimi.execution.AIMICommandLineState
import com.taobao.mc.aimi.ext.services.AIMIPluginService
import com.taobao.mc.aimi.ext.utils.toUriOrNull
import com.taobao.mc.aimi.logger.LoggerManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

@Service(Service.Level.PROJECT)
class GitService(private val project: Project) {
    private val continuePluginService: AIMIPluginService by lazy {
        project.service()
    }
    private val logger = LoggerManager.getLogger(javaClass)

    suspend fun getFileDiff(filepath: String, staged: Boolean = false, deep: Int = 0): String? {
        // val filepath = UriUtils.uriToVirtualFile(filepath)?.toUriOrNull() ?: return null
        // 通过file拿到对应的git根目录
        val gitRoot = findGitRootViaCommand(filepath) ?: return null
        val commandList = listOf("git", "diff")
        val generalCommandLine = GeneralCommandLine(commandList)
            .withWorkDirectory(gitRoot)
            .apply {
                addParameter("-w")
                if (staged) {
                    addParameter("--staged")
                }
                addParameter(filepath)
            }
        val output = withContext(Dispatchers.IO) {
            ExecUtil.execAndGetOutput(generalCommandLine, 3000)
        }
        logger.debug("output: ${output.stdout}")
        return if (output.exitCode == 0) {
            output.stdout.trim().ifEmpty {
                if (deep > 1) ""
                else getFileDiff(filepath, staged = !staged, deep + 1)
            }
        } else {
            null
        }
    }

    /**
     * Returns the git diff for all workspace directories
     */
    suspend fun getDiff(includeUnstaged: Boolean): List<String> {
        val workspaceDirs = workspaceDirectories()
        val diffs = mutableListOf<String>()

        for (workspaceDir in workspaceDirs) {
            val commandList = buildList {
                addAll(listOf("git", "diff", "--submodule=diff"))
                if (!includeUnstaged) {
                    add("--cached")
                }
            }

            val generalCommandLine = GeneralCommandLine(commandList)
                .withWorkDirectory(UriUtils.uriToFileSafe(workspaceDir))
            runCatching {
                val output = withContext(Dispatchers.IO) {
                    ExecUtil.execAndGetOutput(generalCommandLine)
                }
                diffs.add(output.stdout)
            }.onFailure {
                logger.warn("Error getting diff for workspace dir: ${workspaceDir}, error: ${it.message}")
            }
        }

        return diffs
    }

    private fun workspaceDirectories(): Array<String> {
        val dirs = this.continuePluginService.workspacePaths

        if (dirs?.isNotEmpty() == true) {
            return dirs
        }

        return listOfNotNull(project.guessProjectDir()?.toUriOrNull()).toTypedArray()
    }

    private suspend fun findGitRootViaCommand(startPath: String): String? {
        val command = listOf("git", "rev-parse", "--show-toplevel")

        // 1. 确定工作目录
        val startFile = File(startPath)
        if (!startFile.exists()) {
            logger.warn("Error: Provided path does not exist: $startPath")
            return null
        }
        val workingDir = if (startFile.isDirectory) startFile else startFile.parentFile

        if (workingDir == null) {
            logger.warn("Error: Could not determine a working directory from: $startPath")
            return null
        }

        // 2. 执行命令并获取结果
        val commandLine = AIMICommandLineState.createShellCommandLine(command.joinToString(" "), workingDir.absolutePath)
        val processOutput = withContext(Dispatchers.IO) {
            ExecUtil.execAndGetOutput(commandLine, 3000)
        }
        if (processOutput.exitCode != 0) {
            logger.warn("Error: Git command failed in '$workingDir'. Error: ${processOutput.stderr}")
            return null
        }
        if (processOutput.stdout.isEmpty()) {
            logger.warn("Error: Git command output is empty")
            return null
        }
        logger.debug("processOutput: ${processOutput.stdout}")
        return processOutput.stdout.trim()
    }

}