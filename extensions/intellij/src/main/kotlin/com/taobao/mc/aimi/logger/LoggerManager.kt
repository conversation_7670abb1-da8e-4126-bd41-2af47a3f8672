package com.taobao.mc.aimi.logger

import com.intellij.openapi.diagnostic.LogLevel
import com.intellij.openapi.diagnostic.Logger
import kotlin.properties.ReadOnlyProperty
import kotlin.reflect.KProperty

object LoggerManager {
    private val isDebugMode = System.getenv("USE_TCP")?.toBoolean() ?: false
    private const val MODULE = "AIMI-IDE"

    fun getLogger(tag: String): Logger {
        return Logger.getInstance("${MODULE}.${tag}").apply {
            if (isDebugMode) setLevel(LogLevel.DEBUG)
        }
    }

    fun getLogger(clazz: Class<*>): Logger {
        return getLogger(clazz.simpleName)
    }
}

val logger: ReadOnlyProperty<Any, Logger> get() = LoggerDelegate()

class LoggerDelegate : ReadOnlyProperty<Any, Logger> {
    lateinit var logger: Logger

    override fun getValue(thisRef: Any, property: KProperty<*>): Logger {
        if (!::logger.isInitialized) {
            logger = LoggerManager.getLogger(thisRef.javaClass)
        }
        return logger
    }
}