package com.taobao.mc.aimi.util

import com.intellij.openapi.application.runReadAction
import com.intellij.openapi.command.CommandProcessor
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.project.ProjectManager
import com.intellij.openapi.vfs.VfsUtil
import com.intellij.openapi.vfs.isFile
import com.intellij.openapi.vfs.readText
import com.intellij.util.application
import com.taobao.mc.aimi.ext.FileType
import com.taobao.mc.aimi.ext.core.UriUtils

class FileUtils {
    // todo: use VFS (it's moved from IntellijIde)

    fun fileExists(uri: String): Boolean {
        val file = UriUtils.uriToFileSafe(uri)
        return file?.exists() == true
    }

    /**
     * 写入文件内容，支持撤回操作
     * @param uri 文件URI
     * @param contents 文件内容
     * @param project 项目实例，用于支持撤回操作。如果为null，将尝试获取当前打开的项目
     */
    fun writeFile(uri: String, contents: String, project: Project? = null) {
        application.runWriteAction {
            val virtualFile = UriUtils.uriToVirtualFile(uri) ?: run {
                // 如果虚拟文件不存在，先创建物理文件
                val file = UriUtils.uriToFile(uri)
                file.parentFile?.mkdirs()
                file.writeText(contents)
                // 刷新 VFS 以让 IntelliJ 感知新文件
                VfsUtil.findFileByIoFile(file, true)
            }

            virtualFile?.let { vFile ->
                val document = FileDocumentManager.getInstance().getDocument(vFile)
                if (document != null) {
                    // 获取项目实例，优先使用传入的project参数
                    val targetProject = project ?: ProjectManager.getInstance().openProjects.firstOrNull()

                    // 使用 CommandProcessor 包装文档修改操作，确保支持撤回
                    CommandProcessor.getInstance().executeCommand(targetProject, {
                        document.setText(contents)
                    }, "AIMI File Write", "AIMI")

                    FileDocumentManager.getInstance().saveDocument(document)
                } else {
                    // 直接修改虚拟文件内容（不支持撤回）
                    vFile.setBinaryContent(contents.toByteArray(vFile.charset))
                }
            }
        }
    }

    fun listDir(dir: String): List<List<Any>> {
        // val isValidDir = continuePluginService.workspacePaths?.any { dir.startsWith(it) } ?: false
        // if (!isValidDir) {
        //     logger.warn("listDir---isInValidDir---$dir")
        // }
        val files = UriUtils.uriToFileSafe(dir)?.listFiles()?.map {
            listOf(it.name, if (it.isDirectory) FileType.DIRECTORY.value else FileType.FILE.value)
        } ?: emptyList()

        return files
    }

    fun readFile(uri: String): String {
        return try {
            val virtualFile = UriUtils.uriToVirtualFile(uri)
            val isModifiedFile = virtualFile?.let {
                it.isFile && FileDocumentManager.getInstance().isFileModified(it)
            } ?: false
            val content = if (isModifiedFile) {
                runReadAction {
                    FileDocumentManager.getInstance().getDocument(virtualFile)?.text
                }
            } else null
            content ?: virtualFile?.readText() ?: run {
                val file = UriUtils.uriToFile(uri)
                if (!file.exists() || file.isDirectory) return ""
                file.bufferedReader().readText()
            }
        } catch (e: Exception) {
            e.printStackTrace()
            ""
        }
    }


}